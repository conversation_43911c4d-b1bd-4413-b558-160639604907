const mongoose = require('mongoose');
require('dotenv').config();

async function performanceTest() {
  await mongoose.connect(process.env.MONGODB_URI);
  const db = mongoose.connection.db;
  const collection = db.collection('products');
  
  const testQueries = ['芒果', '酸奶', '伊利', '牛奶', '饼干'];
  const iterations = 5; // 每个查询测试5次
  
  console.log('=== 搜索性能对比测试 ===\n');
  
  for (const query of testQueries) {
    console.log(`测试查询: "${query}"`);
    
    // 基础查询条件
    const baseQuery = {
      status: 'active',
      isVisible: true
    };
    
    // 1. 只使用正则表达式搜索
    const regexTimes = [];
    const searchRegex = new RegExp(query.trim().replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'i');
    const regexSearchQuery = {
      $or: [
        { 'name.display': searchRegex },
        { 'name.english': searchRegex },
        { 'name.chinese': searchRegex },
        { 'category.primary.display': searchRegex },
        { 'category.secondary.display': searchRegex },
        { 'platform.display': searchRegex },
        { 'manufacturer.display': searchRegex },
        { 'specification.display': searchRegex },
        { 'flavor.display': searchRegex }
      ],
      ...baseQuery
    };
    
    for (let i = 0; i < iterations; i++) {
      const startTime = Date.now();
      const regexResults = await collection.find(regexSearchQuery)
        .sort({ collectTime: -1 })
        .limit(50)
        .toArray();
      const endTime = Date.now();
      regexTimes.push(endTime - startTime);
    }
    
    // 2. 只使用文本搜索
    const textTimes = [];
    const textSearchQuery = {
      $text: { $search: query },
      ...baseQuery
    };
    
    for (let i = 0; i < iterations; i++) {
      const startTime = Date.now();
      const textResults = await collection.find(textSearchQuery, { score: { $meta: 'textScore' } })
        .sort({ score: { $meta: 'textScore' }, collectTime: -1 })
        .limit(50)
        .toArray()
        .catch(() => []);
      const endTime = Date.now();
      textTimes.push(endTime - startTime);
    }
    
    // 3. 并行搜索（当前策略）
    const parallelTimes = [];
    
    for (let i = 0; i < iterations; i++) {
      const startTime = Date.now();
      const [textResults, regexResults] = await Promise.all([
        collection.find(textSearchQuery, { score: { $meta: 'textScore' } })
          .sort({ score: { $meta: 'textScore' }, collectTime: -1 })
          .limit(50)
          .toArray()
          .catch(() => []),
        
        collection.find(regexSearchQuery)
          .sort({ collectTime: -1 })
          .limit(50)
          .toArray()
          .catch(() => [])
      ]);
      
      // 合并去重
      const mergedResults = new Map();
      textResults.forEach((product) => {
        mergedResults.set(product.productId, {
          ...product,
          searchType: 'text',
          textScore: product.score || 0
        });
      });
      
      regexResults.forEach((product) => {
        if (!mergedResults.has(product.productId)) {
          mergedResults.set(product.productId, {
            ...product,
            searchType: 'regex',
            textScore: 0
          });
        }
      });
      
      const finalResults = Array.from(mergedResults.values());
      const endTime = Date.now();
      parallelTimes.push(endTime - startTime);
    }
    
    // 计算平均时间
    const avgRegex = regexTimes.reduce((a, b) => a + b, 0) / regexTimes.length;
    const avgText = textTimes.reduce((a, b) => a + b, 0) / textTimes.length;
    const avgParallel = parallelTimes.reduce((a, b) => a + b, 0) / parallelTimes.length;
    
    console.log(`  只用正则: ${avgRegex.toFixed(1)}ms (${regexTimes.join(', ')}ms)`);
    console.log(`  只用文本: ${avgText.toFixed(1)}ms (${textTimes.join(', ')}ms)`);
    console.log(`  并行搜索: ${avgParallel.toFixed(1)}ms (${parallelTimes.join(', ')}ms)`);
    
    // 性能对比
    const regexVsParallel = ((avgRegex - avgParallel) / avgParallel * 100).toFixed(1);
    const textVsParallel = ((avgText - avgParallel) / avgParallel * 100).toFixed(1);
    
    console.log(`  性能对比:`);
    console.log(`    正则 vs 并行: ${regexVsParallel > 0 ? '+' : ''}${regexVsParallel}%`);
    console.log(`    文本 vs 并行: ${textVsParallel > 0 ? '+' : ''}${textVsParallel}%`);
    console.log('');
  }
  
  await mongoose.disconnect();
}

performanceTest().catch(console.error);
